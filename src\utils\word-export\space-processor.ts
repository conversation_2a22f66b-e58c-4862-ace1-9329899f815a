/**
 * 空格处理上下文
 */
interface SpaceContext {
  shouldPreserveSpaces: boolean;
  reason?: string;
}

/**
 * 智能空格处理器
 *
 * 负责根据HTML上下文智能处理各种空格类型，包括：
 * - 填空题空格（下划线内）
 * - 选择题括号内空格
 * - 读音标注对齐空格
 * - Word特殊空格（mso-spacerun）
 * - 普通文本空格（遵循HTML显示原则）
 */
export class SpaceProcessor {
  private readonly NODE_TYPES = {
    ELEMENT_NODE: 1,
    TEXT_NODE: 3,
  };

  /**
   * 处理文本节点的空格
   * @param node 文本节点
   * @returns 处理后的文本
   */
  processTextNodeSpaces(node: Node): string {
    const originalText = node.textContent || '';

    // 分析空格处理上下文
    const spaceContext = this.analyzeSpaceContext(node);

    // 标准化空格处理
    return this.normalizeSpaces(originalText, spaceContext);
  }

  /**
   * 分析空格处理上下文
   * @param node 文本节点
   * @returns 空格处理上下文
   */
  private analyzeSpaceContext(node: Node): SpaceContext {
    const originalText = node.textContent || '';

    // 检查是否在下划线标签内（填空位置）
    if (this.isInUnderlineTag(node)) {
      return {
        shouldPreserveSpaces: true,
        reason: 'underline-fillblank',
      };
    }

    // 检查是否在括号内（选择题答案位置）
    if (this.isInBrackets(node)) {
      return {
        shouldPreserveSpaces: true,
        reason: 'brackets-answer',
      };
    }

    // 检查是否有mso-spacerun属性（Word特殊空格）
    if (this.hasMsoSpacerun(node)) {
      return {
        shouldPreserveSpaces: true,
        reason: 'mso-spacerun',
      };
    }

    // 检查是否是对齐用的空格（如读音标注）
    if (this.isAlignmentSpaces(node, originalText)) {
      return {
        shouldPreserveSpaces: true,
        reason: 'alignment',
      };
    }

    // 默认情况：普通文本空格，遵循HTML显示原则
    return {
      shouldPreserveSpaces: false,
      reason: 'normal-text',
    };
  }

  /**
   * 标准化空格处理
   * @param text 原始文本
   * @param context 空格处理上下文
   * @returns 处理后的文本
   */
  private normalizeSpaces(text: string, context: SpaceContext): string {
    // 第一步：统一空格类型，避免显示过宽
    const result = text
      // 将HTML实体空格转换为普通空格
      .replace(/&nbsp;/g, ' ')
      // 将全角空格转换为普通空格
      .replace(/\u3000/g, ' ');

    // 第二步：根据上下文决定是否合并空格
    if (context.shouldPreserveSpaces) {
      // 保留空格数量的场景：填空、括号内、特殊对齐等
      return result;
    } else {
      // 普通文本：遵循HTML显示原则，合并连续空格
      return result.replace(/\s+/g, ' ');
    }
  }

  /**
   * 检查节点是否在下划线标签内（填空位置）
   * @param node 文本节点
   * @returns 是否在下划线标签内
   */
  private isInUnderlineTag(node: Node): boolean {
    let currentNode = node.parentNode;
    while (
      currentNode &&
      currentNode.nodeType === this.NODE_TYPES.ELEMENT_NODE
    ) {
      const element = currentNode as Element;
      if (element.tagName === 'U') {
        return true;
      }
      currentNode = currentNode.parentNode;
    }
    return false;
  }

  /**
   * 检查节点是否在括号内
   * @param node 文本节点
   * @returns 是否在括号内
   */
  private isInBrackets(node: Node): boolean {
    const parentElement = node.parentElement;
    if (!parentElement) return false;

    // 检查前后兄弟节点是否有括号
    const prevSibling = parentElement.previousSibling;
    const nextSibling = parentElement.nextSibling;

    const hasPrevBracket = prevSibling?.textContent?.includes('(');
    const hasNextBracket = nextSibling?.textContent?.includes(')');

    return !!(hasPrevBracket && hasNextBracket);
  }

  /**
   * 检查节点是否有mso-spacerun属性
   * @param node 文本节点
   * @returns 是否有mso-spacerun属性
   */
  private hasMsoSpacerun(node: Node): boolean {
    const parentElement = node.parentElement;
    if (!parentElement) return false;

    const style = parentElement.getAttribute('style') || '';
    return style.includes('mso-spacerun');
  }

  /**
   * 检查是否是对齐用的空格
   * @param node 文本节点
   * @param text 文本内容
   * @returns 是否是对齐用的空格
   */
  private isAlignmentSpaces(node: Node, text: string): boolean {
    // 如果文本主要由空格组成，且在特定上下文中，可能是对齐用的
    const spaceRatio = (text.match(/\s/g) || []).length / text.length;

    if (spaceRatio > 0.5) {
      const parentElement = node.parentElement;
      if (!parentElement) return false;

      // 检查是否在读音标注等特殊上下文中
      const parentText = parentElement.textContent || '';
      const hasPhoneticMarkers =
        /[àáâãäåāăąèéêëēĕėęěìíîïīĭįıòóôõöøōŏőùúûüūŭůűų]/.test(parentText);
      const hasParentheses =
        parentText.includes('(') && parentText.includes(')');

      return hasPhoneticMarkers || hasParentheses;
    }

    return false;
  }

  /**
   * 获取空格处理统计信息（用于调试）
   * @param node 文本节点
   * @returns 处理统计信息
   */
  getProcessingStats(node: Node): {
    originalText: string;
    processedText: string;
    context: SpaceContext;
    spaceCount: {
      original: number;
      processed: number;
    };
  } {
    const originalText = node.textContent || '';
    const context = this.analyzeSpaceContext(node);
    const processedText = this.normalizeSpaces(originalText, context);

    return {
      originalText,
      processedText,
      context,
      spaceCount: {
        original: (originalText.match(/\s/g) || []).length,
        processed: (processedText.match(/\s/g) || []).length,
      },
    };
  }
}
